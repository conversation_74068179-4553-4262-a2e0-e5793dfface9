import React, { useState, useEffect, useRef } from 'react';
import { Play, Square, RotateCcw, Send, Maximize2 } from 'lucide-react';
import { useTerminal } from '../contexts/TerminalContext';

const TerminalView = ({ project, session }) => {
  const { 
    startTerminal, 
    sendInput, 
    killTerminal, 
    getTerminalBySessionId,
    clearTerminalOutput 
  } = useTerminal();
  
  const [inputValue, setInputValue] = useState('');
  const [isStarting, setIsStarting] = useState(false);
  const [error, setError] = useState('');
  const terminalRef = useRef(null);
  const inputRef = useRef(null);

  const terminal = getTerminalBySessionId(session.id);

  // Auto-scroll to bottom when new output arrives
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [terminal?.output]);

  // Focus input when terminal is active
  useEffect(() => {
    if (terminal && inputRef.current) {
      inputRef.current.focus();
    }
  }, [terminal]);

  const handleStartSession = async () => {
    setIsStarting(true);
    setError('');

    try {
      await startTerminal(
        project.id,
        session.id,
        project.path,
        session.name,
        session.prompt
      );
    } catch (err) {
      setError(err.message);
    } finally {
      setIsStarting(false);
    }
  };

  const handleStopSession = async () => {
    if (terminal) {
      try {
        await killTerminal(terminal.id);
      } catch (err) {
        setError(err.message);
      }
    }
  };

  const handleSendInput = async (e) => {
    e.preventDefault();
    
    if (!inputValue.trim() || !terminal) return;

    try {
      await sendInput(terminal.id, inputValue + '\r');
      setInputValue('');
    } catch (err) {
      setError(err.message);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendInput(e);
    }
  };

  const handleClearTerminal = () => {
    if (terminal) {
      clearTerminalOutput(terminal.id);
    }
  };

  const formatOutput = (output) => {
    // Basic ANSI escape sequence removal for display
    // In a production app, you'd want a proper ANSI parser
    return output.replace(/\x1b\[[0-9;]*m/g, '');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'text-green-400';
      case 'starting':
        return 'text-yellow-400';
      case 'exited':
        return 'text-red-400';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusText = (terminal) => {
    if (!terminal) return 'Not started';

    switch (terminal.status) {
      case 'running':
        return 'Running';
      case 'starting':
        return 'Starting...';
      case 'exited':
        return `Exited (code: ${terminal.exitCode || 'unknown'})`;
      case 'error':
        return `Error: ${terminal.error || 'Unknown error'}`;
      default:
        return terminal.status || 'Unknown';
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">{project.name}</h1>
            <p className="text-sm text-gray-400">{project.path}</p>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm font-medium">{session.name}</span>
              <span className={`text-xs ${getStatusColor(terminal?.status)}`}>
                • {getStatusText(terminal)}
              </span>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {!terminal ? (
              <button
                onClick={handleStartSession}
                disabled={isStarting}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:opacity-50 rounded-lg transition-colors"
              >
                <Play size={16} />
                {isStarting ? 'Starting...' : 'Start Session'}
              </button>
            ) : (
              <>
                <button
                  onClick={handleClearTerminal}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                  title="Clear terminal"
                >
                  <RotateCcw size={16} />
                </button>
                <button
                  onClick={handleStopSession}
                  className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                >
                  <Square size={16} />
                  Stop
                </button>
              </>
            )}
          </div>
        </div>

        {error && (
          <div className="mt-3 p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
            {error}
          </div>
        )}

        {terminal && terminal.status === 'error' && (
          <div className="mt-3 p-3 bg-red-900 border border-red-700 rounded-lg text-red-200 text-sm">
            <strong>Terminal Error:</strong> {terminal.error}
          </div>
        )}

        {terminal && terminal.status === 'exited' && (
          <div className="mt-3 p-3 bg-yellow-900 border border-yellow-700 rounded-lg text-yellow-200 text-sm">
            <strong>Session Exited:</strong> The terminal session exited with code {terminal.exitCode || 'unknown'}.
            You can try starting a new session.
          </div>
        )}
      </div>

      {/* Terminal Output */}
      <div className="flex-1 flex flex-col bg-terminal-bg">
        {!terminal ? (
          <div className="flex-1 flex items-center justify-center text-gray-400">
            <div className="text-center">
              <div className="text-4xl mb-4">⚡</div>
              <p>Click "Start Session" to begin using Gemini CLI</p>
              {session.prompt && (
                <p className="text-sm mt-2 text-gray-500">
                  Initial prompt: "{session.prompt}"
                </p>
              )}
            </div>
          </div>
        ) : (
          <>
            {/* Terminal Output Area */}
            <div
              ref={terminalRef}
              className="flex-1 p-4 overflow-y-auto font-mono text-sm text-terminal-text scrollbar-thin"
            >
              <pre className="terminal-output whitespace-pre-wrap">
                {formatOutput(terminal.output || '')}
              </pre>
            </div>

            {/* Input Area */}
            <div className="border-t border-gray-700 p-4">
              <form onSubmit={handleSendInput} className="flex gap-2">
                <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Type your command or message..."
                  className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                  disabled={terminal.status !== 'running'}
                />
                <button
                  type="submit"
                  disabled={!inputValue.trim() || terminal.status !== 'running'}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded-lg transition-colors flex items-center gap-2"
                >
                  <Send size={16} />
                </button>
              </form>
              <p className="text-xs text-gray-500 mt-2">
                Press Enter to send • Use Gemini CLI commands like /help, /clear, etc.
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TerminalView;
