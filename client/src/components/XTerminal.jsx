import React, { useEffect, useRef } from 'react';
import { Terminal } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import '@xterm/xterm/css/xterm.css';

const XTerminal = ({ output, onInput, disabled = false }) => {
  const terminalRef = useRef(null);
  const xtermRef = useRef(null);
  const fitAddonRef = useRef(null);

  useEffect(() => {
    if (!terminalRef.current) return;

    // Create terminal instance
    const terminal = new Terminal({
      theme: {
        background: '#1a1a1a',
        foreground: '#ffffff',
        cursor: '#ffffff',
        selection: '#ffffff30',
        black: '#000000',
        red: '#ff5555',
        green: '#50fa7b',
        yellow: '#f1fa8c',
        blue: '#bd93f9',
        magenta: '#ff79c6',
        cyan: '#8be9fd',
        white: '#bfbfbf',
        brightBlack: '#4d4d4d',
        brightRed: '#ff6e67',
        brightGreen: '#5af78e',
        brightYellow: '#f4f99d',
        brightBlue: '#caa9fa',
        brightMagenta: '#ff92d0',
        brightCyan: '#9aedfe',
        brightWhite: '#e6e6e6'
      },
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      fontSize: 14,
      lineHeight: 1.2,
      cursorBlink: true,
      cursorStyle: 'block',
      scrollback: 1000,
      convertEol: true,
      disableStdin: disabled
    });

    // Create fit addon
    const fitAddon = new FitAddon();
    terminal.loadAddon(fitAddon);

    // Open terminal
    terminal.open(terminalRef.current);

    // Store references first
    xtermRef.current = terminal;
    fitAddonRef.current = fitAddon;

    // Fit after a small delay to ensure DOM is ready
    setTimeout(() => {
      try {
        fitAddon.fit();
      } catch (error) {
        console.warn('Failed to fit terminal:', error);
      }
    }, 100);

    // Handle input
    terminal.onData((data) => {
      console.log('Terminal input:', data, 'disabled:', disabled);
      if (!disabled && onInput) {
        onInput(data);
      }
    });

    // Handle resize
    const handleResize = () => {
      if (fitAddonRef.current && xtermRef.current) {
        try {
          fitAddonRef.current.fit();
        } catch (error) {
          console.warn('Failed to resize terminal:', error);
        }
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      if (xtermRef.current) {
        xtermRef.current.dispose();
      }
    };
  }, []);

  // Handle output updates
  const lastOutputRef = useRef('');

  useEffect(() => {
    if (xtermRef.current && output) {
      // Only write new content to avoid flickering
      const newContent = output.slice(lastOutputRef.current.length);
      if (newContent) {
        xtermRef.current.write(newContent);
        lastOutputRef.current = output;
      }
    }
  }, [output]);

  // Handle disabled state
  useEffect(() => {
    if (xtermRef.current) {
      xtermRef.current.options.disableStdin = disabled;
    }
  }, [disabled]);

  return (
    <div 
      ref={terminalRef} 
      className="w-full h-full"
      style={{ minHeight: '400px' }}
    />
  );
};

export default XTerminal;
