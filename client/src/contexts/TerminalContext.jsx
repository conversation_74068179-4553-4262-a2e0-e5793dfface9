import React, { createContext, useContext, useState, useEffect, useRef } from 'react';

const TerminalContext = createContext();

export const useTerminal = () => {
  const context = useContext(TerminalContext);
  if (!context) {
    throw new Error('useTerminal must be used within a TerminalProvider');
  }
  return context;
};

export const TerminalProvider = ({ children, socket }) => {
  const [terminals, setTerminals] = useState(new Map());
  const [activeTerminalId, setActiveTerminalId] = useState(null);
  const terminalOutputRef = useRef(new Map());

  useEffect(() => {
    if (!socket) return;

    const handleTerminalOutput = (data) => {
      const { terminalId, sessionId, data: output } = data;
      
      // Store output in ref for immediate access
      if (!terminalOutputRef.current.has(terminalId)) {
        terminalOutputRef.current.set(terminalId, '');
      }
      
      const currentOutput = terminalOutputRef.current.get(terminalId);
      const newOutput = currentOutput + output;
      terminalOutputRef.current.set(terminalId, newOutput);

      // Update state
      setTerminals(prev => {
        const newTerminals = new Map(prev);
        const terminal = newTerminals.get(terminalId) || {
          id: terminalId,
          sessionId,
          output: '',
          status: 'running'
        };
        
        terminal.output = newOutput;
        newTerminals.set(terminalId, terminal);
        return newTerminals;
      });
    };

    const handleTerminalExit = (data) => {
      const { terminalId, code } = data;
      
      setTerminals(prev => {
        const newTerminals = new Map(prev);
        const terminal = newTerminals.get(terminalId);
        if (terminal) {
          terminal.status = 'exited';
          terminal.exitCode = code;
          newTerminals.set(terminalId, terminal);
        }
        return newTerminals;
      });
    };

    socket.on('terminal-output', handleTerminalOutput);
    socket.on('terminal-exit', handleTerminalExit);

    return () => {
      socket.off('terminal-output', handleTerminalOutput);
      socket.off('terminal-exit', handleTerminalExit);
    };
  }, [socket]);

  const startTerminal = async (projectId, sessionId, projectPath, sessionName, prompt = '') => {
    try {
      const response = await fetch('/api/terminal/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          sessionId,
          projectPath,
          sessionName,
          prompt,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start terminal');
      }

      const { terminalId } = await response.json();
      
      // Initialize terminal in state
      setTerminals(prev => {
        const newTerminals = new Map(prev);
        newTerminals.set(terminalId, {
          id: terminalId,
          sessionId,
          projectId,
          projectPath,
          sessionName,
          output: '',
          status: 'starting'
        });
        return newTerminals;
      });

      setActiveTerminalId(terminalId);
      return terminalId;
    } catch (err) {
      console.error('Error starting terminal:', err);
      throw err;
    }
  };

  const sendInput = async (terminalId, input) => {
    try {
      const response = await fetch(`/api/terminal/${terminalId}/input`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ input }),
      });

      if (!response.ok) {
        throw new Error('Failed to send input');
      }
    } catch (err) {
      console.error('Error sending input:', err);
      throw err;
    }
  };

  const killTerminal = async (terminalId) => {
    try {
      const response = await fetch(`/api/terminal/${terminalId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to kill terminal');
      }

      setTerminals(prev => {
        const newTerminals = new Map(prev);
        newTerminals.delete(terminalId);
        return newTerminals;
      });

      terminalOutputRef.current.delete(terminalId);

      if (activeTerminalId === terminalId) {
        setActiveTerminalId(null);
      }
    } catch (err) {
      console.error('Error killing terminal:', err);
      throw err;
    }
  };

  const resizeTerminal = async (terminalId, cols, rows) => {
    try {
      const response = await fetch(`/api/terminal/${terminalId}/resize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cols, rows }),
      });

      if (!response.ok) {
        throw new Error('Failed to resize terminal');
      }
    } catch (err) {
      console.error('Error resizing terminal:', err);
    }
  };

  const getTerminal = (terminalId) => {
    return terminals.get(terminalId);
  };

  const getTerminalBySessionId = (sessionId) => {
    for (const terminal of terminals.values()) {
      if (terminal.sessionId === sessionId) {
        return terminal;
      }
    }
    return null;
  };

  const clearTerminalOutput = (terminalId) => {
    terminalOutputRef.current.set(terminalId, '');
    setTerminals(prev => {
      const newTerminals = new Map(prev);
      const terminal = newTerminals.get(terminalId);
      if (terminal) {
        terminal.output = '';
        newTerminals.set(terminalId, terminal);
      }
      return newTerminals;
    });
  };

  const value = {
    terminals: Array.from(terminals.values()),
    activeTerminalId,
    startTerminal,
    sendInput,
    killTerminal,
    resizeTerminal,
    getTerminal,
    getTerminalBySessionId,
    clearTerminalOutput,
    setActiveTerminalId,
  };

  return (
    <TerminalContext.Provider value={value}>
      {children}
    </TerminalContext.Provider>
  );
};
