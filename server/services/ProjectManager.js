const fs = require('fs-extra');
const path = require('path');
const os = require('os');
const { v4: uuidv4 } = require('uuid');

class ProjectManager {
  constructor() {
    this.projectsFile = path.join(os.homedir(), '.gemini', 'gui-projects.json');
    this.ensureProjectsFile();
  }

  async ensureProjectsFile() {
    const geminiDir = path.join(os.homedir(), '.gemini');
    await fs.ensureDir(geminiDir);
    
    if (!await fs.pathExists(this.projectsFile)) {
      await fs.writeJson(this.projectsFile, { projects: [] }, { spaces: 2 });
    }
  }

  async getProjects() {
    try {
      const data = await fs.readJson(this.projectsFile);
      return data.projects || [];
    } catch (error) {
      console.error('Error reading projects file:', error);
      return [];
    }
  }

  async saveProjects(projects) {
    await fs.writeJson(this.projectsFile, { projects }, { spaces: 2 });
  }

  async createProject(name, projectPath) {
    if (!name || !projectPath) {
      throw new Error('Project name and path are required');
    }

    // Validate that the path exists
    if (!await fs.pathExists(projectPath)) {
      throw new Error(`Project path does not exist: ${projectPath}`);
    }

    const projects = await this.getProjects();
    
    // Check if project with same name or path already exists
    const existingProject = projects.find(p => 
      p.name === name || p.path === projectPath
    );
    
    if (existingProject) {
      throw new Error('Project with this name or path already exists');
    }

    const project = {
      id: uuidv4(),
      name,
      path: projectPath,
      sessions: [],
      createdAt: new Date().toISOString(),
      lastAccessed: new Date().toISOString()
    };

    projects.push(project);
    await this.saveProjects(projects);

    return project;
  }

  async deleteProject(projectId) {
    const projects = await this.getProjects();
    const filteredProjects = projects.filter(p => p.id !== projectId);
    
    if (filteredProjects.length === projects.length) {
      throw new Error('Project not found');
    }

    await this.saveProjects(filteredProjects);
  }

  async getProject(projectId) {
    const projects = await this.getProjects();
    return projects.find(p => p.id === projectId);
  }

  async updateProjectAccess(projectId) {
    const projects = await this.getProjects();
    const project = projects.find(p => p.id === projectId);
    
    if (project) {
      project.lastAccessed = new Date().toISOString();
      await this.saveProjects(projects);
    }
  }

  async getSessions(projectId) {
    const project = await this.getProject(projectId);
    return project ? project.sessions : [];
  }

  async createSession(projectId, sessionName, prompt = '') {
    if (!sessionName) {
      throw new Error('Session name is required');
    }

    const projects = await this.getProjects();
    const project = projects.find(p => p.id === projectId);
    
    if (!project) {
      throw new Error('Project not found');
    }

    // Check if session with same name already exists
    const existingSession = project.sessions.find(s => s.name === sessionName);
    if (existingSession) {
      throw new Error('Session with this name already exists');
    }

    const session = {
      id: uuidv4(),
      name: sessionName,
      prompt,
      createdAt: new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      status: 'created' // created, running, stopped
    };

    project.sessions.push(session);
    project.lastAccessed = new Date().toISOString();
    
    await this.saveProjects(projects);

    return session;
  }

  async updateSessionStatus(projectId, sessionId, status) {
    const projects = await this.getProjects();
    const project = projects.find(p => p.id === projectId);
    
    if (!project) {
      throw new Error('Project not found');
    }

    const session = project.sessions.find(s => s.id === sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    session.status = status;
    session.lastAccessed = new Date().toISOString();
    project.lastAccessed = new Date().toISOString();

    await this.saveProjects(projects);
  }

  async deleteSession(projectId, sessionId) {
    const projects = await this.getProjects();
    const project = projects.find(p => p.id === projectId);
    
    if (!project) {
      throw new Error('Project not found');
    }

    const originalLength = project.sessions.length;
    project.sessions = project.sessions.filter(s => s.id !== sessionId);
    
    if (project.sessions.length === originalLength) {
      throw new Error('Session not found');
    }

    await this.saveProjects(projects);
  }

  async getSession(projectId, sessionId) {
    const project = await this.getProject(projectId);
    if (!project) {
      return null;
    }
    
    return project.sessions.find(s => s.id === sessionId);
  }

  // Helper method to check if Gemini CLI is available
  async checkGeminiCLI() {
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
      const gemini = spawn('which', ['gemini']);
      
      gemini.on('exit', (code) => {
        resolve(code === 0);
      });
      
      gemini.on('error', () => {
        resolve(false);
      });
    });
  }

  // Helper method to get Gemini CLI version
  async getGeminiVersion() {
    const { spawn } = require('child_process');
    
    return new Promise((resolve) => {
      const gemini = spawn('gemini', ['--version']);
      let output = '';
      
      gemini.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      gemini.on('exit', (code) => {
        if (code === 0) {
          resolve(output.trim());
        } else {
          resolve(null);
        }
      });
      
      gemini.on('error', () => {
        resolve(null);
      });
    });
  }
}

module.exports = ProjectManager;
