const pty = require('node-pty');
const { v4: uuidv4 } = require('uuid');
const os = require('os');
const path = require('path');

class TerminalManager {
  constructor(io) {
    this.io = io;
    this.terminals = new Map();
    this.sessions = new Map(); // Map session IDs to terminal IDs
  }

  async startSession(projectId, sessionId, projectPath, sessionName, prompt = '') {
    const terminalId = uuidv4();
    
    // Create a sanitized session name for tmux
    const tmuxSessionName = `gemini-${projectId}-${sessionName.replace(/\s+/g, '-').toLowerCase()}`;
    
    // Check if tmux session already exists
    const existingSession = await this.findTmuxSession(tmuxSessionName);
    
    let terminal;
    
    if (existingSession) {
      // Attach to existing tmux session
      terminal = pty.spawn('tmux', ['attach-session', '-t', tmuxSessionName], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        cwd: projectPath || process.cwd(),
        env: process.env
      });
    } else {
      // Create new tmux session and start Gemini CLI
      const geminiCommand = this.buildGeminiCommand(prompt);
      
      terminal = pty.spawn('tmux', [
        'new-session', 
        '-d', 
        '-s', tmuxSessionName,
        '-c', projectPath || process.cwd(),
        geminiCommand
      ], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        cwd: projectPath || process.cwd(),
        env: process.env
      });
      
      // After creating the session, attach to it
      setTimeout(() => {
        terminal.write('tmux attach-session -t ' + tmuxSessionName + '\r');
      }, 1000);
    }

    // Store terminal reference
    this.terminals.set(terminalId, {
      terminal,
      projectId,
      sessionId,
      sessionName,
      tmuxSessionName,
      projectPath,
      createdAt: new Date()
    });

    this.sessions.set(sessionId, terminalId);

    // Handle terminal data
    terminal.on('data', (data) => {
      this.io.emit('terminal-output', {
        terminalId,
        sessionId,
        data
      });
    });

    // Handle terminal exit
    terminal.on('exit', (code) => {
      console.log(`Terminal ${terminalId} exited with code ${code}`);
      this.terminals.delete(terminalId);
      this.sessions.delete(sessionId);
      
      this.io.emit('terminal-exit', {
        terminalId,
        sessionId,
        code
      });
    });

    return terminalId;
  }

  async findTmuxSession(sessionName) {
    return new Promise((resolve) => {
      const checkSession = pty.spawn('tmux', ['has-session', '-t', sessionName], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        env: process.env
      });

      checkSession.on('exit', (code) => {
        resolve(code === 0); // Session exists if exit code is 0
      });
    });
  }

  buildGeminiCommand(prompt = '') {
    let command = 'gemini';
    
    if (prompt && prompt.trim()) {
      // Escape the prompt for shell safety
      const escapedPrompt = prompt.replace(/'/g, "'\"'\"'");
      command += ` --prompt '${escapedPrompt}'`;
    }
    
    return command;
  }

  async sendInput(terminalId, input) {
    const terminalData = this.terminals.get(terminalId);
    if (!terminalData) {
      throw new Error(`Terminal ${terminalId} not found`);
    }

    terminalData.terminal.write(input);
  }

  async killSession(terminalId) {
    const terminalData = this.terminals.get(terminalId);
    if (!terminalData) {
      throw new Error(`Terminal ${terminalId} not found`);
    }

    // Kill the tmux session
    try {
      const killSession = pty.spawn('tmux', ['kill-session', '-t', terminalData.tmuxSessionName], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        env: process.env
      });
      
      killSession.on('exit', () => {
        // Clean up terminal
        if (terminalData.terminal) {
          terminalData.terminal.kill();
        }
        this.terminals.delete(terminalId);
        this.sessions.delete(terminalData.sessionId);
      });
    } catch (error) {
      console.error('Error killing tmux session:', error);
      // Fallback: just kill the terminal
      terminalData.terminal.kill();
      this.terminals.delete(terminalId);
      this.sessions.delete(terminalData.sessionId);
    }
  }

  getTerminalBySessionId(sessionId) {
    const terminalId = this.sessions.get(sessionId);
    return terminalId ? this.terminals.get(terminalId) : null;
  }

  getAllTerminals() {
    return Array.from(this.terminals.entries()).map(([id, data]) => ({
      id,
      projectId: data.projectId,
      sessionId: data.sessionId,
      sessionName: data.sessionName,
      projectPath: data.projectPath,
      createdAt: data.createdAt
    }));
  }

  resizeTerminal(terminalId, cols, rows) {
    const terminalData = this.terminals.get(terminalId);
    if (terminalData && terminalData.terminal) {
      terminalData.terminal.resize(cols, rows);
    }
  }
}

module.exports = TerminalManager;
