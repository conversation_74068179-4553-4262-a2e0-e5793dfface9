const pty = require('node-pty');
const { v4: uuidv4 } = require('uuid');
const os = require('os');
const path = require('path');

class TerminalManager {
  constructor(io) {
    this.io = io;
    this.terminals = new Map();
    this.sessions = new Map(); // Map session IDs to terminal IDs
  }

  async startSession(projectId, sessionId, projectPath, sessionName, prompt = '') {
    const terminalId = uuidv4();

    // Create a sanitized session name for tmux
    const tmuxSessionName = `gemini-${projectId}-${sessionName.replace(/\s+/g, '-').toLowerCase()}`;

    console.log(`[TerminalManager] Starting session: ${terminalId}`);
    console.log(`[TerminalManager] Project: ${projectId}, Session: ${sessionId}`);
    console.log(`[TerminalManager] Tmux session name: ${tmuxSessionName}`);
    console.log(`[TerminalManager] Project path: ${projectPath}`);
    console.log(`[TerminalManager] Prompt: ${prompt}`);

    // Check if tmux session already exists
    const existingSession = await this.findTmuxSession(tmuxSessionName);
    console.log(`[TerminalManager] Existing tmux session found: ${existingSession}`);

    let terminal;

    if (existingSession) {
      console.log(`[TerminalManager] Attaching to existing tmux session: ${tmuxSessionName}`);
      // Attach to existing tmux session
      terminal = pty.spawn('tmux', ['attach-session', '-t', tmuxSessionName], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        cwd: projectPath || process.cwd(),
        env: process.env
      });
    } else {
      console.log(`[TerminalManager] Creating new tmux session: ${tmuxSessionName}`);

      // First, create a detached tmux session with a shell
      const createSession = pty.spawn('tmux', [
        'new-session',
        '-d',
        '-s', tmuxSessionName,
        '-c', projectPath || process.cwd(),
        'bash'
      ], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        cwd: projectPath || process.cwd(),
        env: process.env
      });

      // Wait for session creation to complete
      await new Promise((resolve) => {
        createSession.on('exit', () => {
          console.log(`[TerminalManager] Tmux session ${tmuxSessionName} created`);
          resolve();
        });
      });

      // Now start Gemini CLI in the session
      const geminiCommand = this.buildGeminiCommand(prompt);
      console.log(`[TerminalManager] Starting Gemini CLI in session: ${geminiCommand}`);

      const startGemini = pty.spawn('tmux', [
        'send-keys',
        '-t', tmuxSessionName,
        geminiCommand,
        'Enter'
      ], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        env: process.env
      });

      // Wait for Gemini to start
      await new Promise((resolve) => {
        startGemini.on('exit', () => {
          console.log(`[TerminalManager] Gemini CLI started in session`);
          resolve();
        });
      });

      // Now attach to the session
      console.log(`[TerminalManager] Attaching to tmux session: ${tmuxSessionName}`);
      terminal = pty.spawn('tmux', [
        'attach-session',
        '-t', tmuxSessionName
      ], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        cwd: projectPath || process.cwd(),
        env: process.env
      });

      console.log(`[TerminalManager] Attached to tmux session: ${tmuxSessionName}`);
    }

    // Store terminal reference
    this.terminals.set(terminalId, {
      terminal,
      projectId,
      sessionId,
      sessionName,
      tmuxSessionName,
      projectPath,
      createdAt: new Date()
    });

    this.sessions.set(sessionId, terminalId);

    // Setup terminal event handlers
    this.setupTerminalHandlers(terminalId, terminal, sessionId);

    return terminalId;
  }

  setupTerminalHandlers(terminalId, terminal, sessionId) {
    console.log(`[TerminalManager] Setting up handlers for terminal: ${terminalId}`);

    // Handle terminal data
    terminal.on('data', (data) => {
      console.log(`[TerminalManager] Data from terminal ${terminalId}:`, data.substring(0, 100));
      this.io.emit('terminal-output', {
        terminalId,
        sessionId,
        data
      });
    });

    // Handle terminal exit
    terminal.on('exit', (code) => {
      console.log(`[TerminalManager] Terminal ${terminalId} exited with code ${code}`);

      // Don't immediately delete the terminal data - the tmux session might still be running
      const terminalData = this.terminals.get(terminalId);
      if (terminalData) {
        terminalData.status = 'exited';
        terminalData.exitCode = code;
      }

      this.io.emit('terminal-exit', {
        terminalId,
        sessionId,
        code
      });
    });

    // Handle terminal errors
    terminal.on('error', (error) => {
      console.error(`[TerminalManager] Terminal ${terminalId} error:`, error);
      this.io.emit('terminal-error', {
        terminalId,
        sessionId,
        error: error.message
      });
    });
  }

  async findTmuxSession(sessionName) {
    console.log(`[TerminalManager] Checking if tmux session exists: ${sessionName}`);

    return new Promise((resolve) => {
      const checkSession = pty.spawn('tmux', ['has-session', '-t', sessionName], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        env: process.env
      });

      checkSession.on('exit', (code) => {
        const exists = code === 0;
        console.log(`[TerminalManager] Tmux session ${sessionName} exists: ${exists} (exit code: ${code})`);
        resolve(exists);
      });

      checkSession.on('error', (error) => {
        console.error(`[TerminalManager] Error checking tmux session:`, error);
        resolve(false);
      });
    });
  }

  buildGeminiCommand(prompt = '') {
    let command = 'gemini';

    if (prompt && prompt.trim()) {
      // Escape the prompt for shell safety
      const escapedPrompt = prompt.replace(/'/g, "'\"'\"'");
      command += ` --prompt '${escapedPrompt}'`;
    }

    console.log(`[TerminalManager] Built Gemini command: ${command}`);
    return command;
  }

  async sendInput(terminalId, input) {
    const terminalData = this.terminals.get(terminalId);
    if (!terminalData) {
      throw new Error(`Terminal ${terminalId} not found`);
    }

    terminalData.terminal.write(input);
  }

  async killSession(terminalId) {
    const terminalData = this.terminals.get(terminalId);
    if (!terminalData) {
      throw new Error(`Terminal ${terminalId} not found`);
    }

    // Kill the tmux session
    try {
      const killSession = pty.spawn('tmux', ['kill-session', '-t', terminalData.tmuxSessionName], {
        name: 'xterm-color',
        cols: 80,
        rows: 24,
        env: process.env
      });
      
      killSession.on('exit', () => {
        // Clean up terminal
        if (terminalData.terminal) {
          terminalData.terminal.kill();
        }
        this.terminals.delete(terminalId);
        this.sessions.delete(terminalData.sessionId);
      });
    } catch (error) {
      console.error('Error killing tmux session:', error);
      // Fallback: just kill the terminal
      terminalData.terminal.kill();
      this.terminals.delete(terminalId);
      this.sessions.delete(terminalData.sessionId);
    }
  }

  getTerminalBySessionId(sessionId) {
    const terminalId = this.sessions.get(sessionId);
    return terminalId ? this.terminals.get(terminalId) : null;
  }

  getAllTerminals() {
    return Array.from(this.terminals.entries()).map(([id, data]) => ({
      id,
      projectId: data.projectId,
      sessionId: data.sessionId,
      sessionName: data.sessionName,
      projectPath: data.projectPath,
      createdAt: data.createdAt
    }));
  }

  resizeTerminal(terminalId, cols, rows) {
    const terminalData = this.terminals.get(terminalId);
    if (terminalData && terminalData.terminal) {
      terminalData.terminal.resize(cols, rows);
    }
  }
}

module.exports = TerminalManager;
